#!/usr/bin/env python
"""
Mobile App Automation Tool - Entry Point
"""
import os
import sys
import signal
import subprocess
import time
import logging
import argparse
import glob
import shutil
from pathlib import Path

# Add app directory to path
app_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app')
if app_dir not in sys.path:
    sys.path.insert(0, app_dir)

# Project root directory to path for config
root_dir = os.path.dirname(os.path.abspath(__file__))
if root_dir not in sys.path:
    sys.path.insert(0, root_dir)

# Import after adding app directory to path
sys.path.append(app_dir)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Set JAVA_HOME environment variable if not set correctly
try:
    # Check if JAVA_HOME is set and valid
    java_home = os.environ.get('JAVA_HOME')
    if not java_home or not os.path.exists(java_home):
        # Try to find Java home using /usr/libexec/java_home
        if sys.platform == 'darwin':  # macOS
            try:
                java_home_process = subprocess.run(
                    ['/usr/libexec/java_home'],
                    check=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                java_home = java_home_process.stdout.strip()
                if java_home and os.path.exists(java_home):
                    os.environ['JAVA_HOME'] = java_home
                    logger.info(f"Set JAVA_HOME to {java_home}")
                    print(f"Set JAVA_HOME to {java_home}")
            except Exception as e:
                logger.warning(f"Failed to set JAVA_HOME: {e}")
                print(f"Warning: Failed to set JAVA_HOME: {e}")
except Exception as e:
    logger.warning(f"Error checking/setting JAVA_HOME: {e}")
    print(f"Warning: Error checking/setting JAVA_HOME: {e}")

# Function to clean screenshots directory
def clean_screenshots_directory():
    """Delete all screenshots in the app/static/screenshots directory"""
    screenshots_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app', 'static', 'screenshots')
    if os.path.exists(screenshots_dir):
        logger.info(f"Cleaning screenshots directory: {screenshots_dir}")
        try:
            # Delete all files in the directory
            files = glob.glob(os.path.join(screenshots_dir, '*'))
            for file in files:
                if os.path.isfile(file):
                    os.remove(file)
                    logger.debug(f"Deleted screenshot: {file}")
            logger.info(f"Cleaned {len(files)} screenshots")
        except Exception as e:
            logger.error(f"Error cleaning screenshots directory: {e}")
    else:
        logger.warning(f"Screenshots directory not found: {screenshots_dir}")
        # Create the directory if it doesn't exist
        try:
            os.makedirs(screenshots_dir, exist_ok=True)
            logger.info(f"Created screenshots directory: {screenshots_dir}")
        except Exception as e:
            logger.error(f"Error creating screenshots directory: {e}")

# Function to start Appium server
def start_appium_server(port=4723):
    """Start Appium server with inspector plugin and CORS enabled"""
    logger.info(f"Starting Appium server on port {port} with inspector plugin and CORS enabled...")
    try:
        # Build the command
        cmd = [
            'appium',
            '--port', str(port),
            '--use-plugins=inspector',
            '--allow-cors'
        ]

        # Start Appium as a background process
        appium_log = open('appium_server.log', 'w')
        process = subprocess.Popen(
            cmd,
            stdout=appium_log,
            stderr=appium_log,
            text=True
        )

        # Wait a bit to ensure Appium has started
        time.sleep(5)

        # Check if the process is still running
        if process.poll() is None:
            logger.info(f"Appium server started successfully on port {port}")
            return process
        else:
            logger.error(f"Appium server failed to start. Check appium_server.log for details.")
            return None
    except Exception as e:
        logger.error(f"Error starting Appium server: {e}")
        return None

# Function to start iproxy
def start_iproxy(device_port=8100, local_port=8100):
    """Start iproxy to forward iOS device port to local port"""
    logger.info(f"Starting iproxy to forward device port {device_port} to local port {local_port}...")
    try:
        # Build the command
        cmd = [
            'iproxy',
            str(local_port),
            str(device_port)
        ]

        # Start iproxy as a background process
        iproxy_log = open('iproxy.log', 'w')
        process = subprocess.Popen(
            cmd,
            stdout=iproxy_log,
            stderr=iproxy_log,
            text=True
        )

        # Wait a bit to ensure iproxy has started
        time.sleep(2)

        # Check if the process is still running
        if process.poll() is None:
            logger.info(f"iproxy started successfully (device port {device_port} -> local port {local_port})")
            return process
        else:
            logger.error(f"iproxy failed to start. Check iproxy.log for details.")
            return None
    except Exception as e:
        logger.error(f"Error starting iproxy: {e}")
        return None

# Function to kill existing processes
def kill_existing_processes(force_kill=False):
    """
    Kill existing Appium and iproxy processes

    Args:
        force_kill (bool): If True, kill processes regardless of port configuration.
                          If False, only kill if using default ports.
    """
    if not force_kill:
        logger.info("Skipping process termination when using custom ports for multi-instance support")
        return

    logger.info("Killing any existing Appium and iproxy processes...")
    try:
        if sys.platform == 'win32':
            # Windows
            subprocess.run(['taskkill', '/F', '/IM', 'node.exe'],
                          stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=False)
        else:
            # macOS/Linux
            subprocess.run(['pkill', '-f', 'appium'],
                          stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=False)
            subprocess.run(['pkill', '-f', 'iproxy'],
                          stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=False)
        time.sleep(2)  # Wait for processes to terminate
        logger.info("Existing processes terminated")
    except Exception as e:
        logger.error(f"Error killing processes: {e}")

# Signal handler for graceful shutdown
def signal_handler(sig, frame):
    print("\nShutting down and cleaning up...")
    if 'device_controller' in globals() and device_controller:
        device_controller.shutdown()
    sys.exit(0)

if __name__ == '__main__':
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description='Mobile App Automation Tool (Android)')
    parser.add_argument('--port', type=int, default=8080, help='Port to run the Flask server on (default: 8080)')
    parser.add_argument('--appium-port', type=int, default=4723, help='Port for Appium server (default: 4723)')
    parser.add_argument('--wda-port', type=int, default=8100, help='Port for WebDriverAgent (default: 8100)')
    args = parser.parse_args()

    # Set global port configuration
    import config
    config.FLASK_PORT = args.port
    config.APPIUM_PORT = args.appium_port
    # Note: Don't override WDA_PORT for multi-device support - let device controller read device-specific ports
    # config.WDA_PORT = args.wda_port

    # Update APPIUM_CONFIG with the new port
    config.APPIUM_CONFIG['PORT'] = args.appium_port

    # Update FlaskConfig with the new port
    config.FlaskConfig.PORT = args.port

    # Set instance-specific database paths to avoid conflicts between multiple instances
    if args.port != 8080:  # Only for non-default ports
        import os
        instance_suffix = f"_port_{args.port}"

        # Set environment variables for database paths that will be used by the app
        os.environ['INSTANCE_DB_SUFFIX'] = instance_suffix
        os.environ['INSTANCE_PORT'] = str(args.port)

        logger.info(f"Using instance-specific database paths with suffix: {instance_suffix}")
    else:
        # Clear any existing instance-specific environment variables for default port
        import os
        os.environ.pop('INSTANCE_DB_SUFFIX', None)
        os.environ.pop('INSTANCE_PORT', None)

    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Only kill existing processes if using default ports (to avoid conflicts in multi-instance setup)
    using_default_ports = (args.port == 8080 and args.appium_port == 4723 and args.wda_port == 8100)
    if using_default_ports:
        logger.info("Using default ports - killing existing processes to avoid conflicts")
        kill_existing_processes(force_kill=True)
    else:
        logger.info(f"Using custom ports (Flask: {args.port}, Appium: {args.appium_port}, WDA: {args.wda_port}) - preserving existing processes for multi-instance support")
        kill_existing_processes(force_kill=False)

    # Now import the app after setting configuration
    # Import using the app directory that's already in sys.path
    import sys
    import os

    # Temporarily add the app_android directory to the path for imports
    app_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app_android')
    if app_path not in sys.path:
        sys.path.insert(0, app_path)

    # Import the Flask app and device controller
    import app as flask_app
    from app_android.utils.appium_device_controller import AppiumDeviceController
    app = flask_app.app

    # Initialize the device controller with the configured Appium port
    # Note: Don't pass wda_port here for multi-device support - let controller read device-specific ports from wda_ports.txt
    device_controller = AppiumDeviceController(appium_port=args.appium_port)
    # Make it available to the app
    flask_app.device_controller = device_controller

    print(f"Starting Mobile App Automation Tool (Android)...")
    print(f"Configuration:")
    print(f"  - Flask server port: {args.port}")
    print(f"  - Appium server port: {args.appium_port}")
    print(f"  - WebDriverAgent port: {args.wda_port}")
    print(f"Open your web browser and navigate to: http://localhost:{args.port}")
    # Disable reloader to prevent state loss during development testing
    app.run(debug=True, use_reloader=False, host='0.0.0.0', port=args.port)
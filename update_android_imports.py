#!/usr/bin/env python3
"""
<PERSON>ript to update all imports in app_android folder to use app_android namespace
"""
import os
import re
from pathlib import Path

def update_imports_in_file(file_path):
    """Update imports in a single Python file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Update imports from app. to app_android.
        content = re.sub(r'from app\.', 'from app_android.', content)
        
        # Update imports from utils. to app_android.utils.
        content = re.sub(r'from utils\.', 'from app_android.utils.', content)
        
        # Update imports from actions. to app_android.actions.
        content = re.sub(r'from actions\.', 'from app_android.actions.', content)
        
        # Update imports from routes. to app_android.routes.
        content = re.sub(r'from routes\.', 'from app_android.routes.', content)
        
        # Update standalone imports
        content = re.sub(r'^from test_suites_manager import', 'from app_android.test_suites_manager import', content, flags=re.MULTILINE)
        
        # Update import statements that import modules directly
        content = re.sub(r'import utils\.', 'import app_android.utils.', content)
        content = re.sub(r'import actions\.', 'import app_android.actions.', content)
        content = re.sub(r'import routes\.', 'import app_android.routes.', content)
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Updated imports in: {file_path}")
            return True
        else:
            print(f"No changes needed in: {file_path}")
            return False
            
    except Exception as e:
        print(f"Error updating {file_path}: {e}")
        return False

def update_all_imports():
    """Update imports in all Python files in app_android folder"""
    app_android_dir = Path('app_android')
    
    if not app_android_dir.exists():
        print("app_android directory not found!")
        return
    
    updated_files = []
    
    # Find all Python files recursively
    for py_file in app_android_dir.rglob('*.py'):
        if update_imports_in_file(py_file):
            updated_files.append(str(py_file))
    
    print(f"\nUpdated {len(updated_files)} files:")
    for file_path in updated_files:
        print(f"  - {file_path}")

if __name__ == '__main__':
    update_all_imports()

import json
from pathlib import Path
from config import DIRECTORIES
import os
import cv2
import numpy as np
import base64
from io import BytesIO
from datetime import datetime

def save_json(data, relative_path):
    """Save JSON data to a file"""
    path = DIRECTORIES['BASE_DIR'] / relative_path
    with open(path, 'w') as f:
        json.dump(data, f, indent=2)
    return path

def load_json(relative_path):
    """Load JSON data from a file"""
    path = DIRECTORIES['BASE_DIR'] / relative_path
    with open(path, 'r') as f:
        return json.load(f)

def ensure_directory(relative_path):
    """Ensure a directory exists"""
    path = DIRECTORIES['BASE_DIR'] / relative_path
    path.mkdir(parents=True, exist_ok=True)
    return path

def ensure_dir_exists(directory):
    """Ensure that a directory exists, creating it if necessary"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        
def get_timestamp():
    """Get current timestamp in a format suitable for filenames"""
    return datetime.now().strftime('%Y%m%d%H%M%S')

def save_to_file(content, filepath):
    """Save content to a file, creating directories if needed"""
    directory = os.path.dirname(filepath)
    ensure_dir_exists(directory)
    
    with open(filepath, 'w') as f:
        f.write(content)
        
def read_from_file(filepath):
    """Read content from a file"""
    if not os.path.exists(filepath):
        return None
        
    with open(filepath, 'r') as f:
        return f.read()

def compare_screenshots(screenshot1, screenshot2, threshold=1.0):
    """Compare two screenshots to detect changes
    
    Args:
        screenshot1: Path to first screenshot
        screenshot2: Path to second screenshot
        threshold: Percentage threshold for considering images different (1.0 = 1%)
        
    Returns:
        bool: True if screenshots are different, False if they are similar
    """
    try:
        # Get full paths if needed
        if not os.path.isabs(screenshot1):
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            screenshot1 = os.path.join(base_dir, 'app', screenshot1)
            
        if not os.path.isabs(screenshot2):
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            screenshot2 = os.path.join(base_dir, 'app', screenshot2)
                            
        if not os.path.exists(screenshot1) or not os.path.exists(screenshot2):
            print(f"Screenshot files missing: {screenshot1} or {screenshot2}")
            return False
                
        # Use simple file comparison first (faster)
        if os.path.getsize(screenshot1) != os.path.getsize(screenshot2):
            return True
                
        # Use image comparison (more accurate but slower)
        img1 = cv2.imread(screenshot1)
        img2 = cv2.imread(screenshot2)
            
        if img1 is None or img2 is None:
            print("Could not read images")
            return False
                
        # Convert to grayscale
        gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)
            
        # Calculate difference
        diff = cv2.absdiff(gray1, gray2)
        diff_pixels = np.count_nonzero(diff > 30)
            
        # Calculate percentage of different pixels
        total_pixels = diff.shape[0] * diff.shape[1]
        diff_percentage = (diff_pixels / total_pixels) * 100
            
        print(f"Screenshot difference: {diff_percentage:.2f}%")
            
        # Consider changed if more than the threshold percentage of pixels are different
        return diff_percentage > threshold
    except Exception as e:
        print(f"Error comparing screenshots: {e}")
        return False

def base64_to_image(base64_str):
    """Convert a base64 string to an image object"""
    try:
        # Decode base64 string
        img_data = base64.b64decode(base64_str)
        
        # Create a BytesIO object from the decoded data
        buffer = BytesIO(img_data)
        
        # Read the image using OpenCV
        nparr = np.frombuffer(buffer.getvalue(), np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        return img
    except Exception as e:
        print(f"Error converting base64 to image: {e}")
        return None

def image_to_base64(image):
    """Convert an image to base64 string"""
    try:
        # Encode image as PNG
        success, buffer = cv2.imencode('.png', image)
        if not success:
            return None
            
        # Convert to base64
        return base64.b64encode(buffer).decode('utf-8')
    except Exception as e:
        print(f"Error converting image to base64: {e}")
        return None 
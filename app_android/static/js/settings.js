// Settings and Configuration Manager

// Define loadTestCases as a global function
let loadTestCases;

document.addEventListener('DOMContentLoaded', function() {
    console.log("Settings manager initialized");

    // Get elements
    const testcasesDirInput = document.getElementById('testCasesDir');
    const reportsDirInput = document.getElementById('reportsDir');
    const refImagesDirInput = document.getElementById('referenceImagesDir');
    const filesToPushDirInput = document.getElementById('filesToPushDir');
    const testSuitesDirInput = document.getElementById('testSuitesDir');

    const validateTestCasesBtn = document.getElementById('validateTestCasesBtn');
    const validateReportsBtn = document.getElementById('validateReportsBtn');
    const validateRefImagesBtn = document.getElementById('validateRefImagesBtn');
    const validateFilesToPushBtn = document.getElementById('validateFilesToPushBtn');
    const validateTestSuitesBtn = document.getElementById('validateTestSuitesBtn');

    const testRunRetryInput = document.getElementById('testRunRetry');
    const testCaseDelayInput = document.getElementById('testCaseDelay');
    const stepDelayInput = document.getElementById('stepDelay');
    const hookDelayInput = document.getElementById('hookDelay');
    const retryDelayInput = document.getElementById('retryDelay');
    const maxStepExecutionTimeInput = document.getElementById('maxStepExecutionTime');
    const autoRerunFailedInput = document.getElementById('autoRerunFailed');

    const resetSettingsBtn = document.getElementById('resetSettingsBtn');
    const saveSettingsBtn = document.getElementById('saveSettingsBtn');

    // Tab handlers - load appropriate data when tabs are shown
    document.getElementById('settings-tab-btn').addEventListener('click', function() {
        loadDirectories();
        loadGlobalValues(); // Make sure we load global values too
    });

    document.getElementById('test-cases-tab-btn').addEventListener('click', function() {
        loadTestCases();
    });

    // Directory validation buttons
    if (validateTestCasesBtn) {
        validateTestCasesBtn.addEventListener('click', function() {
            validateDirectory('testcases_dir', testcasesDirInput.value, 'testCasesValidationResult');
        });
    }

    if (validateReportsBtn) {
        validateReportsBtn.addEventListener('click', function() {
            validateDirectory('reports_dir', reportsDirInput.value, 'reportsValidationResult');
        });
    }

    if (validateRefImagesBtn) {
        validateRefImagesBtn.addEventListener('click', function() {
            validateDirectory('reference_images_dir', refImagesDirInput.value, 'refImagesValidationResult');
        });
    }

    if (validateFilesToPushBtn) {
        validateFilesToPushBtn.addEventListener('click', function() {
            validateDirectory('files_to_push_dir', filesToPushDirInput.value, 'filesToPushValidationResult');
        });
    }

    if (validateTestSuitesBtn) {
        validateTestSuitesBtn.addEventListener('click', function() {
            validateDirectory('test_suites_dir', testSuitesDirInput.value, 'testSuitesValidationResult');
        });
    }

    // Global values save button
    const saveGlobalValuesBtn = document.getElementById('saveGlobalValuesBtn');
    if (saveGlobalValuesBtn) {
        saveGlobalValuesBtn.addEventListener('click', function() {
            saveGlobalValues();
        });
    }

    // Settings buttons
    if (resetSettingsBtn) {
        resetSettingsBtn.addEventListener('click', function() {
            resetDirectories();
        });
    }

    if (saveSettingsBtn) {
        saveSettingsBtn.addEventListener('click', function() {
            saveDirectories();
        });
    }

    // Load directories when settings page is opened
    function loadDirectories() {
        console.log("Loading directories configuration");
        fetch('/api/directory_paths')
            .then(response => response.json())
            .then(data => {
                console.log("Directories loaded:", data);
                if (testcasesDirInput) testcasesDirInput.value = data.test_cases_dir || '';
                if (reportsDirInput) reportsDirInput.value = data.reports_dir || '';
                if (refImagesDirInput) refImagesDirInput.value = data.reference_images_dir || '';
                if (filesToPushDirInput) filesToPushDirInput.value = data.files_to_push_dir || '';
                if (testSuitesDirInput) testSuitesDirInput.value = data.test_suites_dir || '';
            })
            .catch(error => {
                console.error("Error loading directories:", error);
                logAction('Error', 'Failed to load directories: ' + error.message, 'error');
            });
    }

    // Load global values from server
    function loadGlobalValues() {
        console.log("Loading global values configuration");
        fetch('/api/settings')
            .then(response => response.json())
            .then(data => {
                console.log("Global values loaded:", data.global_values);

                // Set the Test Run Retry value if available
                if (testRunRetryInput && data.global_values && 'Test Run Retry' in data.global_values) {
                    testRunRetryInput.value = data.global_values['Test Run Retry'];
                    console.log("Set Test Run Retry value:", data.global_values['Test Run Retry']);
                } else if (testRunRetryInput) {
                    // Set default value
                    testRunRetryInput.value = '2';
                    console.log("Set default Test Run Retry value: 2");
                }

                // Set the Auto Rerun Failed checkbox
                if (autoRerunFailedInput && data.global_values && 'Auto Rerun Failed' in data.global_values) {
                    autoRerunFailedInput.checked = data.global_values['Auto Rerun Failed'];
                    console.log("Set Auto Rerun Failed value:", data.global_values['Auto Rerun Failed']);
                } else if (autoRerunFailedInput) {
                    // Set default value (off)
                    autoRerunFailedInput.checked = false;
                    console.log("Set default Auto Rerun Failed value: false");
                }

                // Set the Test Case Delay value if available
                if (testCaseDelayInput && data.global_values && 'Test Case Delay' in data.global_values) {
                    testCaseDelayInput.value = data.global_values['Test Case Delay'];
                    console.log("Set Test Case Delay value:", data.global_values['Test Case Delay']);
                } else if (testCaseDelayInput) {
                    // Set default value
                    testCaseDelayInput.value = '10';
                    console.log("Set default Test Case Delay value: 10");
                }

                // Set the Step Delay value if available
                if (stepDelayInput && data.global_values && 'Step Delay' in data.global_values) {
                    stepDelayInput.value = data.global_values['Step Delay'];
                    console.log("Set Step Delay value:", data.global_values['Step Delay']);
                } else if (stepDelayInput) {
                    // Set default value
                    stepDelayInput.value = '2';
                    console.log("Set default Step Delay value: 2");
                }

                // Set the Hook Delay value if available
                if (hookDelayInput && data.global_values && 'Hook Delay' in data.global_values) {
                    hookDelayInput.value = data.global_values['Hook Delay'];
                    console.log("Set Hook Delay value:", data.global_values['Hook Delay']);
                } else if (hookDelayInput) {
                    // Set default value
                    hookDelayInput.value = '2';
                    console.log("Set default Hook Delay value: 2");
                }

                // Set the Retry Delay value if available
                if (retryDelayInput && data.global_values && 'Retry Delay' in data.global_values) {
                    retryDelayInput.value = data.global_values['Retry Delay'];
                    console.log("Set Retry Delay value:", data.global_values['Retry Delay']);
                } else if (retryDelayInput) {
                    // Set default value
                    retryDelayInput.value = '2';
                    console.log("Set default Retry Delay value: 2");
                }

                // Set the Max Step Execution Time value if available
                if (maxStepExecutionTimeInput && data.global_values && 'Max Step Execution Time' in data.global_values) {
                    maxStepExecutionTimeInput.value = data.global_values['Max Step Execution Time'];
                    console.log("Set Max Step Execution Time value:", data.global_values['Max Step Execution Time']);
                } else if (maxStepExecutionTimeInput) {
                    // Set default value
                    maxStepExecutionTimeInput.value = '300';
                    console.log("Set default Max Step Execution Time value: 300");
                }

                // Populate the global values table
                populateGlobalValuesTable(data.global_values || {});
            })
            .catch(error => {
                console.error("Error loading global values:", error);
                logAction('Error', 'Failed to load global values: ' + error.message, 'error');
            });
    }

    // Validate a directory
    function validateDirectory(name, path, resultElementId) {
        console.log(`Validating directory: ${name} = ${path}, resultElementId: ${resultElementId}`);
        const resultElement = document.getElementById(resultElementId);

        if (!resultElement) {
            console.error("Result element not found:", resultElementId);
            return;
        }

        console.log("Result element found:", resultElement);

        // Show loading indicator
        resultElement.innerHTML = '<div class="spinner-border spinner-border-sm text-primary" role="status"><span class="visually-hidden">Loading...</span></div> Validating...';
        resultElement.style.display = 'block';
        resultElement.classList.add('validating');

        console.log("Making API call to validate directory");
        fetch('/api/validate_directory', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                name: name,
                path: path,
                just_validate: true
            })
        })
        .then(response => {
            console.log("Validation response status:", response.status);
            return response.json();
        })
        .then(data => {
            console.log("Validation result:", data);
            resultElement.classList.remove('validating');
            resultElement.classList.add('validation-complete');

            if (data.error) {
                // Show error
                resultElement.innerHTML = `
                    <div class="alert alert-danger p-2 mb-0 fade-in">
                        <i class="bi bi-exclamation-triangle-fill"></i> ${data.error}
                    </div>
                `;
                return;
            }

            if (data.counts && data.counts.error) {
                // Show error from counts
                resultElement.innerHTML = `
                    <div class="alert alert-warning p-2 mb-0 fade-in">
                        <i class="bi bi-exclamation-triangle-fill"></i> ${data.counts.error}
                    </div>
                `;
                return;
            }

            // Show success with counts
            let countDetails = '';

            if (name === 'test_cases_dir' && data.counts.test_cases) {
                countDetails = `<strong>${data.counts.test_cases}</strong> test case(s) found`;
            } else if (name === 'reports_dir' && data.counts.reports) {
                countDetails = `<strong>${data.counts.reports}</strong> report(s) found`;
            } else if (name === 'reference_images_dir' && data.counts.images) {
                countDetails = `<strong>${data.counts.images}</strong> image(s) found`;
            } else if (name === 'test_suites_dir' && data.counts.test_suites) {
                countDetails = `<strong>${data.counts.test_suites}</strong> test suite(s) found`;
            } else if (name === 'files_to_push_dir' && data.counts.files) {
                countDetails = `<strong>${data.counts.files}</strong> file(s) found`;
            } else if (data.counts.total_files !== undefined) {
                countDetails = `<strong>${data.counts.total_files}</strong> file(s) found`;
            } else {
                countDetails = 'Directory is valid but empty';
            }

            resultElement.innerHTML = `
                <div class="alert alert-success p-2 mb-0 fade-in">
                    <i class="bi bi-check-circle-fill"></i> <strong>Validation Success!</strong> ${countDetails}
                </div>
            `;

            // Highlight the validation result temporarily
            resultElement.classList.add('highlight-result');
            setTimeout(() => {
                resultElement.classList.remove('highlight-result');
            }, 2000);
        })
        .catch(error => {
            console.error("Error validating directory:", error);
            resultElement.classList.remove('validating');
            resultElement.innerHTML = `
                <div class="alert alert-danger p-2 mb-0 fade-in">
                    <i class="bi bi-exclamation-triangle-fill"></i> Error: ${error.message}
                </div>
            `;
        });
    }

    // Reset directories to defaults
    function resetDirectories() {
        console.log("Resetting directories to defaults");

        // Set default values
        if (testcasesDirInput) testcasesDirInput.value = 'test_cases';
        if (reportsDirInput) reportsDirInput.value = 'reports';
        if (refImagesDirInput) refImagesDirInput.value = 'reference_images';
        if (filesToPushDirInput) filesToPushDirInput.value = 'files_to_push';
        if (testSuitesDirInput) testSuitesDirInput.value = 'test_suites';

        // Clear validation results
        const validationElements = document.querySelectorAll('.validation-result');
        validationElements.forEach(element => {
            element.innerHTML = '';
            element.classList.remove('validation-complete', 'highlight-result', 'validating');
        });

        logAction('Settings', 'Directory settings reset to defaults', 'info');
    }

    // Save directories
    function saveDirectories() {
        console.log("Saving directories configuration");

        // Check if input elements exist
        console.log("Input elements exist:", {
            testcasesDirInput: !!testcasesDirInput,
            reportsDirInput: !!reportsDirInput,
            refImagesDirInput: !!refImagesDirInput,
            filesToPushDirInput: !!filesToPushDirInput,
            testSuitesDirInput: !!testSuitesDirInput
        });

        const directories = {
            test_cases_dir: testcasesDirInput ? testcasesDirInput.value : 'test_cases',
            reports_dir: reportsDirInput ? reportsDirInput.value : 'reports',
            reference_images_dir: refImagesDirInput ? refImagesDirInput.value : 'reference_images',
            files_to_push_dir: filesToPushDirInput ? filesToPushDirInput.value : 'files_to_push',
            test_suites_dir: testSuitesDirInput ? testSuitesDirInput.value : 'test_suites'
        };

        console.log("Saving directories:", directories);

        console.log("Making API call to save directories");
        fetch('/api/directory_paths', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(directories)
        })
        .then(response => {
            console.log("Save directories response status:", response.status);
            return response.json();
        })
        .then(data => {
            console.log("Save directories result:", data);
            if (data.success) {
                logAction('Settings', 'Directories saved successfully', 'success');

                // Validate each directory with the saved path
                validateDirectory('testcases_dir', directories.test_cases_dir, 'testCasesValidationResult');
                validateDirectory('reports_dir', directories.reports_dir, 'reportsValidationResult');
                validateDirectory('reference_images_dir', directories.reference_images_dir, 'refImagesValidationResult');
                validateDirectory('files_to_push_dir', directories.files_to_push_dir, 'filesToPushValidationResult');
                validateDirectory('test_suites_dir', directories.test_suites_dir, 'testSuitesValidationResult');

                // Refresh the test cases list if we're on that tab
                if (document.getElementById('test-cases-tab') &&
                    document.getElementById('test-cases-tab').classList.contains('active')) {
                    loadTestCases();
                }
            } else {
                logAction('Error', data.message || 'Failed to save directories', 'error');
            }
        })
        .catch(error => {
            console.error("Error saving directories:", error);
            logAction('Error', 'Failed to save directories: ' + error.message, 'error');
        });
    }

    // Save global values
    function saveGlobalValues() {
        // Get the global values from the table
        const globalValues = collectGlobalValues();

        // Include the Test Run Retry value
        if (testRunRetryInput) {
            const retryValue = parseInt(testRunRetryInput.value);
            if (!isNaN(retryValue) && retryValue >= 0) {
                globalValues['Test Run Retry'] = retryValue;
            } else {
                globalValues['Test Run Retry'] = 2; // Default value
            }
        }

        // Include the Auto Rerun Failed value
        if (autoRerunFailedInput) {
            globalValues['Auto Rerun Failed'] = autoRerunFailedInput.checked;
        } else {
            globalValues['Auto Rerun Failed'] = false; // Default value
        }

        // Include the Test Case Delay value
        if (testCaseDelayInput) {
            const delayValue = parseInt(testCaseDelayInput.value);
            if (!isNaN(delayValue) && delayValue >= 0) {
                globalValues['Test Case Delay'] = delayValue;
                // Also set the JavaScript-friendly version for the execution manager
                globalValues['test_case_delay'] = delayValue * 1000; // Convert to milliseconds
            } else {
                globalValues['Test Case Delay'] = 10; // Default value
                globalValues['test_case_delay'] = 10000; // Default value in milliseconds
            }
        }

        // Include the Step Delay value
        if (stepDelayInput) {
            const stepDelayValue = parseFloat(stepDelayInput.value);
            if (!isNaN(stepDelayValue) && stepDelayValue >= 0) {
                globalValues['Step Delay'] = stepDelayValue;
                // Also set the JavaScript-friendly version for the execution manager
                globalValues['step_delay'] = stepDelayValue * 1000; // Convert to milliseconds
            } else {
                globalValues['Step Delay'] = 2; // Default value
                globalValues['step_delay'] = 2000; // Default value in milliseconds
            }
        }

        // Include the Hook Delay value
        if (hookDelayInput) {
            const hookDelayValue = parseFloat(hookDelayInput.value);
            if (!isNaN(hookDelayValue) && hookDelayValue >= 0) {
                globalValues['Hook Delay'] = hookDelayValue;
                // Also set the JavaScript-friendly version for the execution manager
                globalValues['hook_delay'] = hookDelayValue * 1000; // Convert to milliseconds
            } else {
                globalValues['Hook Delay'] = 2; // Default value
                globalValues['hook_delay'] = 2000; // Default value in milliseconds
            }
        }

        // Include the Retry Delay value
        if (retryDelayInput) {
            const retryDelayValue = parseFloat(retryDelayInput.value);
            if (!isNaN(retryDelayValue) && retryDelayValue >= 0) {
                globalValues['Retry Delay'] = retryDelayValue;
                // Also set the JavaScript-friendly version for the execution manager
                globalValues['retry_delay'] = retryDelayValue * 1000; // Convert to milliseconds
            } else {
                globalValues['Retry Delay'] = 2; // Default value
                globalValues['retry_delay'] = 2000; // Default value in milliseconds
            }
        }

        // Include the Max Step Execution Time value
        if (maxStepExecutionTimeInput) {
            const maxStepExecutionTimeValue = parseInt(maxStepExecutionTimeInput.value);
            if (!isNaN(maxStepExecutionTimeValue) && maxStepExecutionTimeValue >= 10) {
                globalValues['Max Step Execution Time'] = maxStepExecutionTimeValue;
            } else {
                globalValues['Max Step Execution Time'] = 300; // Default value
            }
        }

        // Save to the server
        fetch('/api/settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                global_values: globalValues
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                logAction('Error', 'Failed to save global values: ' + data.error, 'error');
            } else {
                logAction('Settings', 'Global values saved successfully', 'success');
            }
        })
        .catch(error => {
            console.error("Error saving global values:", error);
            logAction('Error', 'Failed to save global values: ' + error.message, 'error');
        });
    }

    // Collect global values from the table
    function collectGlobalValues() {
        const globalValues = {};
        const rows = document.querySelectorAll('#globalValuesTableBody tr');

        rows.forEach(row => {
            const nameInput = row.querySelector('input[name="globalParamName"]');
            const valueInput = row.querySelector('input[name="globalParamValue"]');

            if (nameInput && valueInput && nameInput.value.trim()) {
                // Try to parse the value if it looks like a number or boolean
                let value = valueInput.value;

                // Check if it's a number
                if (!isNaN(Number(value)) && value.trim() !== '') {
                    // If it's an integer
                    if (value.indexOf('.') === -1) {
                        value = parseInt(value);
                    } else {
                        value = parseFloat(value);
                    }
                }
                // Check if it's a boolean
                else if (value.toLowerCase() === 'true') {
                    value = true;
                }
                else if (value.toLowerCase() === 'false') {
                    value = false;
                }

                globalValues[nameInput.value] = value;
            }
        });

        return globalValues;
    }

    // Test Cases Management - Make this function available globally
    loadTestCases = function() {
        console.log("Loading test cases");
        // Use the correct endpoint that works with TestCaseManager.js
        fetch('/api/recording/list')
            .then(response => response.json())
            .then(data => {
                console.log("Test cases loaded:", data);
                populateTestCasesTable(data.test_cases || []);
            })
            .catch(error => {
                console.error("Error loading test cases:", error);
                logAction('Error', 'Failed to load test cases: ' + error.message, 'error');
            });
    }

    // Make loadTestCases available globally through the window object
    window.loadTestCases = loadTestCases;

    function populateTestCasesTable(testCases) {
        console.log("Populating test cases table with:", testCases);

        // Check if we're in the modal view or the main test cases tab
        const tableBody = document.querySelector('#modalTestCasesTable tbody');

        if (!tableBody) {
            console.log("Modal test cases table not found, this is normal if we're not in the modal view");
            // If we're not in the modal view, we don't need to populate the table
            // The TestCaseManager.js will handle populating the main test cases tab
            return;
        }

        // Clear existing rows
        tableBody.innerHTML = '';

        if (testCases.length === 0) {
            // No test cases
            const row = document.createElement('tr');
            row.innerHTML = '<td colspan="5" class="text-center">No test cases found</td>';
            tableBody.appendChild(row);
            return;
        }

        // Add test cases to table
        testCases.forEach(testCase => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${testCase.name}</td>
                <td>${testCase.description || '-'}</td>
                <td>${formatDate(testCase.created)}</td>
                <td>${testCase.action_count}</td>
                <td>
                    <button class="btn btn-sm btn-primary load-test-case" data-filename="${testCase.filename}">
                        <i class="bi bi-folder-symlink"></i> Load
                    </button>
                    <button class="btn btn-sm btn-danger delete-test-case" data-filename="${testCase.filename}">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            `;
            tableBody.appendChild(row);

            // Add event listeners to buttons
            row.querySelector('.load-test-case').addEventListener('click', function() {
                loadTestCase(testCase.filename);
            });

            row.querySelector('.delete-test-case').addEventListener('click', function() {
                deleteTestCase(testCase.filename);
            });
        });
    }

    function loadTestCase(filename) {
        console.log("Loading test case:", filename);
        fetch(`/api/test_cases/load/${filename}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Handle loading the test case into the UI
                    logAction('Test Case', `Loaded test case: ${data.test_case.name}`, 'success');

                    // Get a reference to the main tab where we'll return focus
                    const deviceTab = document.getElementById('device-tab');

                    // Close the modal and ensure proper focus management
                    const modalElement = document.getElementById('loadTestCaseModal');
                    if (modalElement) {
                        // First remove any active focus inside the modal
                        document.activeElement.blur();

                        // Get Bootstrap modal instance and hide it
                        const modal = bootstrap.Modal.getInstance(modalElement);
                        if (modal) {
                            // Hide the modal
                            modal.hide();

                            // After modal is hidden, move focus to the main tab to avoid focus being trapped
                            modalElement.addEventListener('hidden.bs.modal', function handleHidden() {
                                // Move focus to device tab or other appropriate element
                                if (deviceTab) deviceTab.focus();
                                // Remove this one-time event listener
                                modalElement.removeEventListener('hidden.bs.modal', handleHidden);
                            }, { once: true });
                        }
                    }

                    // Dispatch an event so other modules can handle loading the actions
                    document.dispatchEvent(new CustomEvent('testCaseLoaded', {
                        detail: data.test_case
                    }));
                } else {
                    logAction('Error', data.message || 'Failed to load test case', 'error');
                }
            })
            .catch(error => {
                console.error("Error loading test case:", error);
                logAction('Error', 'Failed to load test case: ' + error.message, 'error');
            });
    }

    function deleteTestCase(filename) {
        if (!confirm(`Are you sure you want to delete the test case "${filename}"?`)) {
            return;
        }

        console.log("Deleting test case:", filename);
        fetch(`/api/delete_test_case/${filename}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                logAction('Test Case', 'Test case deleted successfully', 'success');
                loadTestCases(); // Refresh the list
            } else {
                logAction('Error', data.message || 'Failed to delete test case', 'error');
            }
        })
        .catch(error => {
            console.error("Error deleting test case:", error);
            logAction('Error', 'Failed to delete test case: ' + error.message, 'error');
        });
    }

    // Test Suites Management
    function loadTestSuites() {
        console.log("Loading test suites");
        fetch('/api/test_suites/list')
            .then(response => response.json())
            .then(data => {
                populateTestSuitesTable(data.test_suites || []);
            })
            .catch(error => {
                console.error("Error loading test suites:", error);
                logAction('Error', 'Failed to load test suites: ' + error.message, 'error');
            });
    }

    function populateTestSuitesTable(testSuites) {
        console.log("Populating test suites table with:", testSuites);
        const tableBody = document.querySelector('#test-suites-table tbody');
        if (!tableBody) {
            console.error("Test suites table body not found");
            return;
        }

        // Clear existing rows
        tableBody.innerHTML = '';

        if (testSuites.length === 0) {
            // No test suites
            const row = document.createElement('tr');
            row.innerHTML = '<td colspan="5" class="text-center">No test suites found</td>';
            tableBody.appendChild(row);
            return;
        }

        // Add test suites to table
        testSuites.forEach(suite => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${suite.name}</td>
                <td>${suite.description || '-'}</td>
                <td>${formatDate(suite.updated || suite.created)}</td>
                <td>${suite.test_count}</td>
                <td>
                    <button class="btn btn-sm btn-success execute-suite" data-filename="${suite.filename}">
                        <i class="bi bi-play-fill"></i>
                    </button>
                    <button class="btn btn-sm btn-primary edit-suite" data-filename="${suite.filename}">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-danger delete-suite" data-filename="${suite.filename}">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            `;
            tableBody.appendChild(row);

            // Add event listeners to buttons
        });
    }

    // Helper function for formatting dates
    function formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return isNaN(date.getTime()) ? dateString : date.toLocaleString();
    }

    // Helper function to populate global values table
    function populateGlobalValuesTable(globalValues) {
        const tbody = document.getElementById('globalValuesTableBody');
        if (!tbody) {
            console.error("Global values table body not found");
            return;
        }

        // Clear existing rows
        tbody.innerHTML = '';

        // Add rows for each global value except Test Run Retry (it has its own input)
        Object.entries(globalValues).forEach(([key, value]) => {
            if (key !== 'Test Run Retry') {
                addGlobalValueRow(key, value);
            }
        });

        // Add button to add new parameter
        const addButton = document.getElementById('addGlobalParameterBtn');
        if (addButton) {
            // Remove existing listener to prevent duplicates
            const newAddBtn = addButton.cloneNode(true);
            if (addButton.parentNode) {
                addButton.parentNode.replaceChild(newAddBtn, addButton);
            }

            // Add new listener
            newAddBtn.addEventListener('click', function() {
                addGlobalValueRow('', '');
            });
        }
    }

    // Helper function to add a row to global values table
    function addGlobalValueRow(name = '', value = '') {
        const tbody = document.getElementById('globalValuesTableBody');
        if (!tbody) return;

        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <input type="text" class="form-control param-name" value="${name}" placeholder="Parameter name">
            </td>
            <td>
                <input type="text" class="form-control param-value" value="${value}" placeholder="Parameter value">
            </td>
            <td class="text-center">
                <button type="button" class="btn btn-sm btn-outline-danger delete-param">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        `;

        // Add delete button handler
        const deleteBtn = row.querySelector('.delete-param');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', function() {
                row.remove();
            });
        }

        tbody.appendChild(row);
    }
});